# Node.js生态系统专业知识

## 核心技术栈

### Node.js运行时
- **版本管理**: 使用LTS版本(v18+)确保稳定性
- **事件循环**: 理解单线程事件驱动模型
- **模块系统**: CommonJS和ES Modules的使用
- **包管理**: npm/yarn/pnpm的选择和使用

### Express.js框架
```javascript
// 基础Express应用结构
const express = require('express');
const app = express();

// 中间件配置
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 路由定义
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date() });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Internal Server Error' });
});
```

### 中间件生态
- **cors**: 跨域资源共享
- **helmet**: 安全头部设置
- **morgan**: HTTP请求日志
- **compression**: Gzip压缩
- **rate-limit**: 请求频率限制

## 数据库集成

### MongoDB + Mongoose
```javascript
// Mongoose连接配置
const mongoose = require('mongoose');

mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
});

// Schema定义
const orderSchema = new mongoose.Schema({
  orderId: { type: String, required: true, unique: true },
  sku: { type: String, required: true },
  quantity: { type: Number, required: true, min: 1 },
  price: { type: Number, required: true, min: 0 },
  orderTime: { type: Date, default: Date.now },
  status: { 
    type: String, 
    enum: ['pending', 'paid', 'shipped', 'completed'],
    default: 'pending'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true }
});

// 索引优化
orderSchema.index({ orderId: 1 });
orderSchema.index({ sku: 1, orderTime: -1 });

const Order = mongoose.model('Order', orderSchema);
```

### Redis缓存
```javascript
const redis = require('redis');

// Redis连接配置
const client = redis.createClient({
  host: process.env.REDIS_HOST,
  port: process.env.REDIS_PORT,
  password: process.env.REDIS_PASSWORD,
  db: 0,
  retry_strategy: (options) => {
    if (options.error && options.error.code === 'ECONNREFUSED') {
      return new Error('Redis服务器拒绝连接');
    }
    if (options.total_retry_time > 1000 * 60 * 60) {
      return new Error('重试时间超时');
    }
    return Math.min(options.attempt * 100, 3000);
  }
});

// 缓存操作封装
class CacheService {
  static async get(key) {
    try {
      const value = await client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('缓存读取失败:', error);
      return null;
    }
  }
  
  static async set(key, value, ttl = 3600) {
    try {
      await client.setex(key, ttl, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('缓存写入失败:', error);
      return false;
    }
  }
  
  static async del(key) {
    try {
      await client.del(key);
      return true;
    } catch (error) {
      console.error('缓存删除失败:', error);
      return false;
    }
  }
}
```

## 文件处理

### Multer文件上传
```javascript
const multer = require('multer');
const path = require('path');

// 存储配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

// 文件过滤
const fileFilter = (req, file, cb) => {
  if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
    cb(null, true);
  } else {
    cb(new Error('只支持CSV文件'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1
  }
});
```

### CSV解析处理
```javascript
const csv = require('csv-parser');
const fs = require('fs');

class CSVProcessor {
  static async parseCSV(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      const results = [];
      const errors = [];
      
      fs.createReadStream(filePath)
        .pipe(csv({
          skipEmptyLines: true,
          headers: options.headers || true
        }))
        .on('data', (data) => {
          try {
            // 数据验证和清洗
            const cleanedData = this.cleanData(data);
            if (this.validateData(cleanedData)) {
              results.push(cleanedData);
            } else {
              errors.push({ row: results.length + 1, data, error: '数据验证失败' });
            }
          } catch (error) {
            errors.push({ row: results.length + 1, data, error: error.message });
          }
        })
        .on('end', () => {
          resolve({ data: results, errors });
        })
        .on('error', (error) => {
          reject(error);
        });
    });
  }
  
  static cleanData(data) {
    const cleaned = {};
    
    Object.keys(data).forEach(key => {
      let value = data[key];
      
      // 去除前后空格
      if (typeof value === 'string') {
        value = value.trim();
      }
      
      // 数值类型转换
      if (key.includes('价格') || key.includes('金额')) {
        value = parseFloat(value.replace(/[^\d.]/g, '')) || 0;
      }
      
      // 整数类型转换
      if (key.includes('数量')) {
        value = parseInt(value) || 0;
      }
      
      cleaned[key] = value;
    });
    
    return cleaned;
  }
  
  static validateData(data) {
    // 基础验证逻辑
    return data && Object.keys(data).length > 0;
  }
}
```

## 身份验证与安全

### JWT认证
```javascript
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

class AuthService {
  static generateToken(payload) {
    return jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: '24h',
      issuer: 'ecommerce-tools'
    });
  }
  
  static verifyToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      throw new Error('Token验证失败');
    }
  }
  
  static async hashPassword(password) {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  }
  
  static async comparePassword(password, hash) {
    return await bcrypt.compare(password, hash);
  }
}

// 认证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: '缺少访问令牌' });
  }
  
  try {
    const decoded = AuthService.verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({ error: '无效的访问令牌' });
  }
};
```

## 错误处理与日志

### 全局错误处理
```javascript
class AppError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// 全局错误处理中间件
const globalErrorHandler = (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';
  
  // 开发环境返回详细错误信息
  if (process.env.NODE_ENV === 'development') {
    res.status(err.statusCode).json({
      status: err.status,
      error: err,
      message: err.message,
      stack: err.stack
    });
  } else {
    // 生产环境返回简化错误信息
    if (err.isOperational) {
      res.status(err.statusCode).json({
        status: err.status,
        message: err.message
      });
    } else {
      res.status(500).json({
        status: 'error',
        message: '服务器内部错误'
      });
    }
  }
};
```

### Winston日志系统
```javascript
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'ecommerce-tools' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});

// 开发环境添加控制台输出
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

module.exports = logger;
```

## 性能优化

### 数据库查询优化
```javascript
// 分页查询优化
class QueryBuilder {
  static buildPaginationQuery(page = 1, limit = 20) {
    const skip = (page - 1) * limit;
    return { skip, limit: Math.min(limit, 100) }; // 限制最大页面大小
  }
  
  static buildSortQuery(sortBy = 'createdAt', order = 'desc') {
    const sortOrder = order === 'asc' ? 1 : -1;
    return { [sortBy]: sortOrder };
  }
  
  static buildFilterQuery(filters = {}) {
    const query = {};
    
    // 日期范围过滤
    if (filters.startDate || filters.endDate) {
      query.createdAt = {};
      if (filters.startDate) query.createdAt.$gte = new Date(filters.startDate);
      if (filters.endDate) query.createdAt.$lte = new Date(filters.endDate);
    }
    
    // 文本搜索
    if (filters.search) {
      query.$text = { $search: filters.search };
    }
    
    return query;
  }
}
```

### 缓存策略
```javascript
// 缓存装饰器
function cache(ttl = 3600) {
  return function(target, propertyName, descriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function(...args) {
      const cacheKey = `${target.constructor.name}:${propertyName}:${JSON.stringify(args)}`;
      
      // 尝试从缓存获取
      const cached = await CacheService.get(cacheKey);
      if (cached) {
        return cached;
      }
      
      // 执行原方法
      const result = await method.apply(this, args);
      
      // 存入缓存
      await CacheService.set(cacheKey, result, ttl);
      
      return result;
    };
  };
}

// 使用示例
class OrderService {
  @cache(1800) // 缓存30分钟
  static async getOrderStats(dateRange) {
    // 复杂的统计查询
    return await Order.aggregate([
      { $match: { createdAt: { $gte: dateRange.start, $lte: dateRange.end } } },
      { $group: { _id: null, total: { $sum: '$amount' }, count: { $sum: 1 } } }
    ]);
  }
}
```
