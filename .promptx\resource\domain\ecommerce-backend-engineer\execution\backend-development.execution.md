<execution>
  <constraint>
    ## 后端开发客观约束
    
    ### 技术栈约束
    - **Node.js版本**: 必须使用LTS版本(v18+)，确保稳定性
    - **Express框架**: 基于Express 4.x，遵循中间件模式
    - **数据库限制**: MongoDB文档大小限制16MB，Redis内存限制
    - **文件处理**: CSV文件大小限制，内存使用控制
    
    ### 性能约束
    - **响应时间**: API响应时间不超过2秒
    - **并发处理**: 支持至少1000并发连接
    - **内存使用**: 单进程内存使用不超过512MB
    - **数据库连接**: 连接池大小合理配置
    
    ### 安全约束
    - **数据验证**: 所有输入数据必须验证
    - **权限控制**: 严格的API访问权限控制
    - **数据加密**: 敏感数据必须加密存储
    - **SQL注入防护**: 使用参数化查询
  </constraint>
  
  <rule>
    ## 强制开发规则
    
    ### 代码规范
    - **ESLint规范**: 必须通过ESLint检查
    - **代码格式**: 使用Prettier统一代码格式
    - **命名规范**: 驼峰命名法，语义化命名
    - **注释要求**: 关键逻辑必须有详细注释
    
    ### API设计规则
    - **RESTful规范**: 严格遵循REST API设计原则
    - **HTTP状态码**: 正确使用HTTP状态码
    - **错误处理**: 统一的错误响应格式
    - **版本控制**: API版本号管理
    
    ### 数据处理规则
    - **数据验证**: 使用Joi进行数据验证
    - **事务处理**: 关键操作必须使用事务
    - **缓存策略**: 热点数据必须缓存
    - **日志记录**: 关键操作必须记录日志
    
    ### 安全规则
    - **身份验证**: JWT Token验证
    - **权限检查**: 每个API都要权限检查
    - **数据脱敏**: 敏感数据输出脱敏
    - **HTTPS强制**: 生产环境强制HTTPS
  </rule>
  
  <guideline>
    ## 开发指导原则
    
    ### 架构设计指导
    - **分层架构**: 建议使用Controller-Service-Repository模式
    - **依赖注入**: 推荐使用依赖注入管理模块依赖
    - **配置管理**: 建议使用环境变量管理配置
    - **错误处理**: 推荐全局错误处理中间件
    
    ### 性能优化指导
    - **数据库优化**: 建议合理设计索引，优化查询语句
    - **缓存使用**: 推荐Redis缓存热点数据
    - **异步处理**: 建议使用异步处理提高并发能力
    - **资源管理**: 推荐及时释放资源，避免内存泄漏
    
    ### 开发效率指导
    - **代码复用**: 建议抽取公共模块，提高代码复用率
    - **工具使用**: 推荐使用开发工具提高效率
    - **测试驱动**: 建议编写单元测试和集成测试
    - **文档维护**: 推荐及时更新API文档
  </guideline>
  
  <process>
    ## 后端开发流程
    
    ### 需求分析阶段
    1. **业务需求理解**: 深入理解电商运营工具的业务逻辑
    2. **技术需求分析**: 分析性能、安全、扩展性要求
    3. **接口设计**: 设计RESTful API接口规范
    4. **数据模型设计**: 设计MongoDB文档结构
    
    ### 环境搭建阶段
    1. **开发环境**: 配置Node.js、MongoDB、Redis开发环境
    2. **项目初始化**: 创建项目结构，配置package.json
    3. **依赖安装**: 安装Express、Mongoose、Redis等依赖
    4. **配置文件**: 设置数据库连接、环境变量等配置
    
    ### 核心开发阶段
    1. **数据库连接**: 实现MongoDB和Redis连接管理
    2. **中间件开发**: 实现认证、日志、错误处理中间件
    3. **API开发**: 实现投产比计算、定价计算等核心API
    4. **文件处理**: 实现CSV文件上传、解析、处理功能
    5. **数据分析**: 实现利润分析、销量统计等分析功能
    
    ### 测试验证阶段
    1. **单元测试**: 编写业务逻辑单元测试
    2. **接口测试**: 使用Postman或Jest测试API接口
    3. **集成测试**: 测试前后端集成功能
    4. **性能测试**: 测试API响应时间和并发能力
    
    ### 部署上线阶段
    1. **代码审查**: 代码质量检查和安全审查
    2. **环境配置**: 配置生产环境和监控系统
    3. **部署发布**: 使用PM2部署Node.js应用
    4. **监控运维**: 配置日志监控和性能监控
  </process>
  
  <criteria>
    ## 开发质量标准
    
    ### 代码质量标准
    - **代码覆盖率**: 单元测试覆盖率 ≥ 80%
    - **代码规范**: ESLint检查通过率 = 100%
    - **性能指标**: API响应时间 ≤ 2秒
    - **错误率**: 生产环境错误率 ≤ 0.1%
    
    ### API质量标准
    - **接口规范**: 100%遵循RESTful设计原则
    - **文档完整**: API文档覆盖率 = 100%
    - **错误处理**: 统一错误格式，错误信息清晰
    - **版本管理**: 支持API版本控制和向后兼容
    
    ### 安全质量标准
    - **身份验证**: 100%的受保护接口需要身份验证
    - **数据验证**: 100%的输入数据需要验证
    - **权限控制**: 细粒度的权限控制机制
    - **安全审计**: 定期安全漏洞扫描和修复
    
    ### 性能质量标准
    - **响应时间**: 95%的请求响应时间 ≤ 1秒
    - **并发能力**: 支持1000+并发用户
    - **资源使用**: CPU使用率 ≤ 70%，内存使用率 ≤ 80%
    - **可用性**: 系统可用性 ≥ 99.9%
  </criteria>
</execution>
