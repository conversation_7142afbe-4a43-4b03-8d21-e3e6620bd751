const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('express-async-errors');
require('dotenv').config();

const connectDB = require('./config/database');
const { connectRedis } = require('./config/redis');
const logger = require('./utils/logger');
const { globalErrorHandler } = require('./middleware/errorHandler');
const notFound = require('./middleware/notFound');

// 路由导入
const authRoutes = require('./routes/auth');
const calculationRoutes = require('./routes/calculations');
const fileRoutes = require('./routes/files');
const analysisRoutes = require('./routes/analysis');
const userRoutes = require('./routes/users');

const app = express();

// 信任代理
app.set('trust proxy', 1);

// 安全中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS配置
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// 请求限制
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // 限制每个IP 100个请求
  message: {
    error: '请求过于频繁，请稍后再试',
    code: 429
  },
  standardHeaders: true,
  legacyHeaders: false
});
app.use('/api/', limiter);

// 基础中间件
app.use(compression());
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API路由
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/calculations', calculationRoutes);
app.use('/api/v1/files', fileRoutes);
app.use('/api/v1/analysis', analysisRoutes);
app.use('/api/v1/users', userRoutes);

// API文档路由
app.get('/api/v1', (req, res) => {
  res.json({
    name: '电商运营工具箱 API',
    version: '1.0.0',
    description: '提供投产比计算、定价分析、数据处理等电商运营工具',
    endpoints: {
      auth: '/api/v1/auth',
      calculations: '/api/v1/calculations',
      files: '/api/v1/files',
      analysis: '/api/v1/analysis',
      users: '/api/v1/users'
    },
    documentation: '/api/v1/docs'
  });
});

// 404处理
app.use(notFound);

// 全局错误处理
app.use(globalErrorHandler);

// 数据库连接（暂时注释掉，先让服务器启动）
// connectDB();
// connectRedis();

const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || 'localhost';

const server = app.listen(PORT, HOST, () => {
  logger.info(`🚀 服务器运行在 http://${HOST}:${PORT}`);
  logger.info(`📊 环境: ${process.env.NODE_ENV}`);
  logger.info(`📝 API文档: http://${HOST}:${PORT}/api/v1`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...');
  server.close(() => {
    logger.info('HTTP服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，开始优雅关闭...');
  server.close(() => {
    logger.info('HTTP服务器已关闭');
    process.exit(0);
  });
});

module.exports = app;
