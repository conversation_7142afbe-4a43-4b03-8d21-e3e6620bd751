2025-06-23 14:08:57 [ERROR]: uncaughtException: Cannot find module './routes/auth'
Require stack:
- E:\AI\promptx\vscode\backend\src\app.js
Error: Cannot find module './routes/auth'
Require stack:
- E:\AI\promptx\vscode\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (E:\AI\promptx\vscode\backend\src\app.js:17:20)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module './routes/auth'
Require stack:
- E:\AI\promptx\vscode\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (E:\AI\promptx\vscode\backend\src\app.js:17:20)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
2025-06-23 14:13:25 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (E:\AI\promptx\vscode\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (E:\AI\promptx\vscode\backend\src\app.js:102:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (E:\AI\promptx\vscode\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (E:\AI\promptx\vscode\backend\src\app.js:102:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)
    at node:internal/main/run_main_module:36:49
