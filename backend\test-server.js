const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();

// 基础中间件
app.use(cors());
app.use(express.json());

// 测试路由
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: '服务器运行正常',
    timestamp: new Date().toISOString()
  });
});

// 简单的ROI计算测试
app.post('/api/v1/calculations/roi', (req, res) => {
  try {
    const { adCost, revenue, productCost = 0 } = req.body;
    
    if (!adCost || !revenue) {
      return res.status(400).json({
        code: 400,
        message: '广告费用和销售额不能为空'
      });
    }
    
    const roi = ((revenue - adCost) / adCost) * 100;
    const roas = revenue / adCost;
    const profit = revenue - productCost - adCost;
    
    res.json({
      code: 200,
      message: '计算成功',
      data: {
        roi: parseFloat(roi.toFixed(2)),
        roas: parseFloat(roas.toFixed(2)),
        profit: parseFloat(profit.toFixed(2))
      }
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '计算失败',
      error: error.message
    });
  }
});

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log(`🚀 测试服务器运行在 http://localhost:${PORT}`);
  console.log(`📊 健康检查: http://localhost:${PORT}/health`);
  console.log(`🧮 ROI计算: POST http://localhost:${PORT}/api/v1/calculations/roi`);
});

module.exports = app;
