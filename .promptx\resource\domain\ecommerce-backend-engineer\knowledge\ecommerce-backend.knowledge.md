# 电商后端业务知识

## 电商运营核心业务逻辑

### 投产比(ROI)计算业务
```javascript
class ROICalculationService {
  /**
   * 计算投资回报率
   * @param {Object} params - 计算参数
   * @param {number} params.adCost - 广告费用
   * @param {number} params.revenue - 销售额
   * @param {number} params.productCost - 商品成本
   * @param {string} params.channel - 广告渠道
   * @param {Object} params.timeRange - 时间范围
   */
  static calculateROI(params) {
    const { adCost, revenue, productCost = 0, channel, timeRange } = params;
    
    // 基础验证
    if (adCost <= 0) throw new Error('广告费用必须大于0');
    if (revenue <= 0) throw new Error('销售额必须大于0');
    
    // ROI计算：(收入 - 投入) / 投入 * 100%
    const roi = ((revenue - adCost) / adCost) * 100;
    
    // ROAS计算：收入 / 广告支出
    const roas = revenue / adCost;
    
    // 利润计算
    const grossProfit = revenue - productCost;
    const netProfit = grossProfit - adCost;
    const profitRate = (netProfit / revenue) * 100;
    
    // 渠道效率评估
    const channelEfficiency = this.evaluateChannelEfficiency(channel, roi);
    
    return {
      roi: parseFloat(roi.toFixed(2)),
      roas: parseFloat(roas.toFixed(2)),
      grossProfit: parseFloat(grossProfit.toFixed(2)),
      netProfit: parseFloat(netProfit.toFixed(2)),
      profitRate: parseFloat(profitRate.toFixed(2)),
      channelEfficiency,
      calculatedAt: new Date(),
      timeRange
    };
  }
  
  static evaluateChannelEfficiency(channel, roi) {
    const benchmarks = {
      'taobao': { excellent: 300, good: 200, average: 100 },
      'tmall': { excellent: 250, good: 150, average: 80 },
      'jd': { excellent: 280, good: 180, average: 90 },
      'pdd': { excellent: 400, good: 250, average: 120 }
    };
    
    const benchmark = benchmarks[channel] || benchmarks['taobao'];
    
    if (roi >= benchmark.excellent) return 'excellent';
    if (roi >= benchmark.good) return 'good';
    if (roi >= benchmark.average) return 'average';
    return 'poor';
  }
}
```

### 定价策略计算
```javascript
class PricingCalculationService {
  /**
   * 智能定价计算
   * @param {Object} params - 定价参数
   */
  static calculatePricing(params) {
    const {
      cost,                    // 成本
      targetProfitRate,        // 目标利润率
      operatingCost = 0,       // 运营成本
      platformFee = 0,         // 平台费用
      competitorPrice = null,  // 竞品价格
      marketPosition = 'mid'   // 市场定位: low/mid/high
    } = params;
    
    // 基础验证
    if (cost <= 0) throw new Error('成本必须大于0');
    if (targetProfitRate < 0 || targetProfitRate > 100) {
      throw new Error('目标利润率必须在0-100之间');
    }
    
    // 总成本计算
    const totalCost = cost + operatingCost + platformFee;
    
    // 基础定价计算
    const basePriceByProfit = totalCost / (1 - targetProfitRate / 100);
    
    // 市场定位调整
    const positionMultiplier = {
      'low': 0.9,
      'mid': 1.0,
      'high': 1.2
    };
    
    const adjustedPrice = basePriceByProfit * positionMultiplier[marketPosition];
    
    // 竞品价格分析
    const competitiveAnalysis = this.analyzeCompetitivePrice(
      adjustedPrice, 
      competitorPrice
    );
    
    // 价格策略建议
    const strategies = this.generatePricingStrategies(
      totalCost, 
      adjustedPrice, 
      competitorPrice
    );
    
    return {
      suggestedPrice: parseFloat(adjustedPrice.toFixed(2)),
      strategies,
      competitiveAnalysis,
      profitAnalysis: {
        totalCost,
        grossProfit: adjustedPrice - totalCost,
        profitRate: ((adjustedPrice - totalCost) / adjustedPrice) * 100
      },
      calculatedAt: new Date()
    };
  }
  
  static analyzeCompetitivePrice(ourPrice, competitorPrice) {
    if (!competitorPrice) {
      return { status: 'no_data', message: '无竞品价格数据' };
    }
    
    const priceDiff = ourPrice - competitorPrice;
    const priceDiffPercent = (priceDiff / competitorPrice) * 100;
    
    if (priceDiffPercent > 20) {
      return { 
        status: 'higher', 
        message: '价格偏高，可能影响竞争力',
        suggestion: '考虑降低价格或突出产品差异化'
      };
    } else if (priceDiffPercent < -20) {
      return { 
        status: 'lower', 
        message: '价格偏低，利润空间可能不足',
        suggestion: '可以适当提高价格或降低成本'
      };
    } else {
      return { 
        status: 'competitive', 
        message: '价格具有竞争力',
        suggestion: '保持当前定价策略'
      };
    }
  }
  
  static generatePricingStrategies(cost, basePrice, competitorPrice) {
    return {
      conservative: {
        price: parseFloat((cost * 1.2).toFixed(2)),
        description: '保守策略，确保基本利润',
        profitRate: 16.67
      },
      balanced: {
        price: parseFloat(basePrice.toFixed(2)),
        description: '平衡策略，兼顾利润和竞争力',
        profitRate: ((basePrice - cost) / basePrice) * 100
      },
      aggressive: {
        price: parseFloat((basePrice * 1.15).toFixed(2)),
        description: '激进策略，追求高利润',
        profitRate: ((basePrice * 1.15 - cost) / (basePrice * 1.15)) * 100
      }
    };
  }
}
```

### CSV数据分析引擎
```javascript
class EcommerceDataAnalyzer {
  /**
   * 分析电商订单数据
   * @param {Array} orders - 订单数据数组
   * @param {Object} options - 分析选项
   */
  static analyzeOrderData(orders, options = {}) {
    const {
      groupBy = 'sku',           // 分组维度: sku/category/date
      timeRange = null,          // 时间范围
      includeRefunds = false     // 是否包含退款订单
    } = options;
    
    // 数据预处理
    let filteredOrders = this.preprocessOrders(orders, {
      timeRange,
      includeRefunds
    });
    
    // 基础统计
    const basicStats = this.calculateBasicStats(filteredOrders);
    
    // SKU分析
    const skuAnalysis = this.analyzeSkuPerformance(filteredOrders);
    
    // 利润分析
    const profitAnalysis = this.analyzeProfitability(filteredOrders);
    
    // 趋势分析
    const trendAnalysis = this.analyzeTrends(filteredOrders);
    
    return {
      summary: basicStats,
      skuAnalysis,
      profitAnalysis,
      trendAnalysis,
      generatedAt: new Date()
    };
  }
  
  static preprocessOrders(orders, options) {
    let filtered = [...orders];
    
    // 时间范围过滤
    if (options.timeRange) {
      const { startDate, endDate } = options.timeRange;
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.orderTime);
        return orderDate >= startDate && orderDate <= endDate;
      });
    }
    
    // 退款订单处理
    if (!options.includeRefunds) {
      filtered = filtered.filter(order => 
        order.orderStatus !== 'refunded' && 
        order.orderStatus !== 'cancelled'
      );
    }
    
    // 数据清洗
    filtered = filtered.map(order => ({
      ...order,
      revenue: parseFloat(order.actualAmount || order.totalAmount || 0),
      cost: parseFloat(order.totalCost || order.unitCost * order.quantity || 0),
      profit: parseFloat(order.profit || 0),
      quantity: parseInt(order.quantity || 0)
    }));
    
    return filtered;
  }
  
  static calculateBasicStats(orders) {
    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + order.revenue, 0);
    const totalCost = orders.reduce((sum, order) => sum + order.cost, 0);
    const totalProfit = totalRevenue - totalCost;
    const totalQuantity = orders.reduce((sum, order) => sum + order.quantity, 0);
    
    return {
      totalOrders,
      totalRevenue: parseFloat(totalRevenue.toFixed(2)),
      totalCost: parseFloat(totalCost.toFixed(2)),
      totalProfit: parseFloat(totalProfit.toFixed(2)),
      totalQuantity,
      avgOrderValue: parseFloat((totalRevenue / totalOrders).toFixed(2)),
      avgProfit: parseFloat((totalProfit / totalOrders).toFixed(2)),
      overallProfitRate: parseFloat(((totalProfit / totalRevenue) * 100).toFixed(2))
    };
  }
  
  static analyzeSkuPerformance(orders) {
    const skuMap = new Map();
    
    // 按SKU聚合数据
    orders.forEach(order => {
      const sku = order.sku;
      if (!skuMap.has(sku)) {
        skuMap.set(sku, {
          sku,
          productName: order.productName,
          totalQuantity: 0,
          totalRevenue: 0,
          totalCost: 0,
          totalProfit: 0,
          orderCount: 0
        });
      }
      
      const skuData = skuMap.get(sku);
      skuData.totalQuantity += order.quantity;
      skuData.totalRevenue += order.revenue;
      skuData.totalCost += order.cost;
      skuData.totalProfit += order.profit;
      skuData.orderCount += 1;
    });
    
    // 转换为数组并计算衍生指标
    const skuArray = Array.from(skuMap.values()).map(sku => ({
      ...sku,
      avgOrderValue: sku.totalRevenue / sku.orderCount,
      profitRate: (sku.totalProfit / sku.totalRevenue) * 100,
      revenueShare: 0, // 稍后计算
      quantityShare: 0  // 稍后计算
    }));
    
    // 计算占比
    const totalRevenue = skuArray.reduce((sum, sku) => sum + sku.totalRevenue, 0);
    const totalQuantity = skuArray.reduce((sum, sku) => sum + sku.totalQuantity, 0);
    
    skuArray.forEach(sku => {
      sku.revenueShare = parseFloat(((sku.totalRevenue / totalRevenue) * 100).toFixed(2));
      sku.quantityShare = parseFloat(((sku.totalQuantity / totalQuantity) * 100).toFixed(2));
    });
    
    // 排序
    const sortedByRevenue = [...skuArray].sort((a, b) => b.totalRevenue - a.totalRevenue);
    const sortedByQuantity = [...skuArray].sort((a, b) => b.totalQuantity - a.totalQuantity);
    const sortedByProfit = [...skuArray].sort((a, b) => b.totalProfit - a.totalProfit);
    
    return {
      topByRevenue: sortedByRevenue.slice(0, 10),
      topByQuantity: sortedByQuantity.slice(0, 10),
      topByProfit: sortedByProfit.slice(0, 10),
      all: skuArray
    };
  }
  
  static generateChartData(skuAnalysis, dimension = 'revenue', limit = 10) {
    let data;
    
    switch (dimension) {
      case 'revenue':
        data = skuAnalysis.topByRevenue.slice(0, limit);
        break;
      case 'quantity':
        data = skuAnalysis.topByQuantity.slice(0, limit);
        break;
      case 'profit':
        data = skuAnalysis.topByProfit.slice(0, limit);
        break;
      default:
        data = skuAnalysis.topByRevenue.slice(0, limit);
    }
    
    return data.map(sku => ({
      name: sku.productName || sku.sku,
      value: dimension === 'revenue' ? sku.totalRevenue :
             dimension === 'quantity' ? sku.totalQuantity :
             sku.totalProfit,
      percentage: dimension === 'revenue' ? sku.revenueShare :
                  dimension === 'quantity' ? sku.quantityShare :
                  parseFloat(((sku.totalProfit / data.reduce((sum, s) => sum + s.totalProfit, 0)) * 100).toFixed(2))
    }));
  }
}
```

## 电商平台集成

### 平台数据格式适配器
```javascript
class PlatformDataAdapter {
  static adapters = {
    taobao: {
      fieldMapping: {
        '订单编号': 'orderId',
        '商品标题': 'productName',
        '商品SKU': 'sku',
        '数量': 'quantity',
        '单价': 'unitPrice',
        '实付金额': 'actualAmount',
        '下单时间': 'orderTime',
        '收货人': 'receiver'
      },
      dateFormat: 'YYYY-MM-DD HH:mm:ss',
      currencySymbol: '¥'
    },
    
    tmall: {
      fieldMapping: {
        '订单号': 'orderId',
        '商品名称': 'productName',
        'SKU': 'sku',
        '购买数量': 'quantity',
        '商品单价': 'unitPrice',
        '订单金额': 'actualAmount',
        '创建时间': 'orderTime',
        '收货人姓名': 'receiver'
      },
      dateFormat: 'YYYY/MM/DD HH:mm:ss',
      currencySymbol: '￥'
    },
    
    jd: {
      fieldMapping: {
        '订单号': 'orderId',
        '商品名称': 'productName',
        'SKU编码': 'sku',
        '购买数量': 'quantity',
        '商品单价': 'unitPrice',
        '订单金额': 'actualAmount',
        '下单日期': 'orderTime',
        '收货人姓名': 'receiver'
      },
      dateFormat: 'YYYY-MM-DD HH:mm:ss',
      currencySymbol: '¥'
    }
  };
  
  static detectPlatform(headers) {
    const headerStr = headers.join(',').toLowerCase();
    
    if (headerStr.includes('商品sku') || headerStr.includes('淘宝')) {
      return 'taobao';
    } else if (headerStr.includes('天猫') || headerStr.includes('tmall')) {
      return 'tmall';
    } else if (headerStr.includes('京东') || headerStr.includes('sku编码')) {
      return 'jd';
    }
    
    return 'unknown';
  }
  
  static adaptData(rawData, platform) {
    const adapter = this.adapters[platform];
    if (!adapter) {
      throw new Error(`不支持的平台: ${platform}`);
    }
    
    return rawData.map(row => {
      const adaptedRow = {};
      
      // 字段映射
      Object.entries(adapter.fieldMapping).forEach(([originalField, targetField]) => {
        if (row[originalField] !== undefined) {
          adaptedRow[targetField] = row[originalField];
        }
      });
      
      // 数据类型转换
      this.convertDataTypes(adaptedRow, adapter);
      
      return adaptedRow;
    });
  }
  
  static convertDataTypes(row, adapter) {
    // 数值类型转换
    ['unitPrice', 'actualAmount', 'quantity'].forEach(field => {
      if (row[field]) {
        if (field === 'quantity') {
          row[field] = parseInt(row[field]) || 0;
        } else {
          // 移除货币符号和其他非数字字符
          const cleanValue = row[field].toString()
            .replace(adapter.currencySymbol, '')
            .replace(/[^\d.]/g, '');
          row[field] = parseFloat(cleanValue) || 0;
        }
      }
    });
    
    // 日期类型转换
    if (row.orderTime) {
      row.orderTime = this.parseDate(row.orderTime, adapter.dateFormat);
    }
  }
  
  static parseDate(dateStr, format) {
    // 简化的日期解析，实际项目中建议使用moment.js或date-fns
    try {
      return new Date(dateStr);
    } catch (error) {
      console.warn('日期解析失败:', dateStr);
      return new Date();
    }
  }
}
```

## 业务规则引擎

### 数据验证规则
```javascript
class BusinessRuleEngine {
  static validationRules = {
    order: {
      orderId: {
        required: true,
        type: 'string',
        minLength: 5,
        maxLength: 50
      },
      quantity: {
        required: true,
        type: 'number',
        min: 1,
        max: 10000
      },
      unitPrice: {
        required: true,
        type: 'number',
        min: 0.01,
        max: 1000000
      },
      orderTime: {
        required: true,
        type: 'date',
        maxAge: 365 * 24 * 60 * 60 * 1000 // 一年
      }
    }
  };
  
  static validateOrder(order) {
    const errors = [];
    const rules = this.validationRules.order;
    
    Object.entries(rules).forEach(([field, rule]) => {
      const value = order[field];
      
      // 必填检查
      if (rule.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field}是必填字段`);
        return;
      }
      
      if (value === undefined || value === null) return;
      
      // 类型检查
      if (rule.type === 'number' && (typeof value !== 'number' || isNaN(value))) {
        errors.push(`${field}必须是有效数字`);
        return;
      }
      
      if (rule.type === 'string' && typeof value !== 'string') {
        errors.push(`${field}必须是字符串`);
        return;
      }
      
      if (rule.type === 'date' && !(value instanceof Date) && isNaN(new Date(value))) {
        errors.push(`${field}必须是有效日期`);
        return;
      }
      
      // 范围检查
      if (rule.min !== undefined && value < rule.min) {
        errors.push(`${field}不能小于${rule.min}`);
      }
      
      if (rule.max !== undefined && value > rule.max) {
        errors.push(`${field}不能大于${rule.max}`);
      }
      
      // 长度检查
      if (rule.minLength !== undefined && value.length < rule.minLength) {
        errors.push(`${field}长度不能少于${rule.minLength}个字符`);
      }
      
      if (rule.maxLength !== undefined && value.length > rule.maxLength) {
        errors.push(`${field}长度不能超过${rule.maxLength}个字符`);
      }
      
      // 时间范围检查
      if (rule.maxAge !== undefined && rule.type === 'date') {
        const orderDate = new Date(value);
        const maxDate = new Date(Date.now() - rule.maxAge);
        if (orderDate < maxDate) {
          errors.push(`${field}时间过于久远`);
        }
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
```
