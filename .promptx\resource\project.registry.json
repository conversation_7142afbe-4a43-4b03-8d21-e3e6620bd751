{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-23T05:59:49.081Z", "updatedAt": "2025-06-23T05:59:49.083Z", "resourceCount": 8}, "resources": [{"id": "ecommerce-backend-engineer", "source": "project", "protocol": "role", "name": "Ecommerce Backend Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/ecommerce-backend-engineer/ecommerce-backend-engineer.role.md", "metadata": {"createdAt": "2025-06-23T05:59:49.082Z", "updatedAt": "2025-06-23T05:59:49.082Z", "scannedAt": "2025-06-23T05:59:49.082Z"}}, {"id": "backend-engineering", "source": "project", "protocol": "thought", "name": "Backend Engineering 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ecommerce-backend-engineer/thought/backend-engineering.thought.md", "metadata": {"createdAt": "2025-06-23T05:59:49.082Z", "updatedAt": "2025-06-23T05:59:49.082Z", "scannedAt": "2025-06-23T05:59:49.082Z"}}, {"id": "api-design", "source": "project", "protocol": "execution", "name": "Api Design 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ecommerce-backend-engineer/execution/api-design.execution.md", "metadata": {"createdAt": "2025-06-23T05:59:49.082Z", "updatedAt": "2025-06-23T05:59:49.082Z", "scannedAt": "2025-06-23T05:59:49.082Z"}}, {"id": "backend-development", "source": "project", "protocol": "execution", "name": "Backend Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ecommerce-backend-engineer/execution/backend-development.execution.md", "metadata": {"createdAt": "2025-06-23T05:59:49.082Z", "updatedAt": "2025-06-23T05:59:49.082Z", "scannedAt": "2025-06-23T05:59:49.082Z"}}, {"id": "data-processing", "source": "project", "protocol": "execution", "name": "Data Processing 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ecommerce-backend-engineer/execution/data-processing.execution.md", "metadata": {"createdAt": "2025-06-23T05:59:49.082Z", "updatedAt": "2025-06-23T05:59:49.082Z", "scannedAt": "2025-06-23T05:59:49.082Z"}}, {"id": "database-design", "source": "project", "protocol": "knowledge", "name": "Database Design 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ecommerce-backend-engineer/knowledge/database-design.knowledge.md", "metadata": {"createdAt": "2025-06-23T05:59:49.083Z", "updatedAt": "2025-06-23T05:59:49.083Z", "scannedAt": "2025-06-23T05:59:49.083Z"}}, {"id": "ecommerce-backend", "source": "project", "protocol": "knowledge", "name": "Ecommerce Backend 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ecommerce-backend-engineer/knowledge/ecommerce-backend.knowledge.md", "metadata": {"createdAt": "2025-06-23T05:59:49.083Z", "updatedAt": "2025-06-23T05:59:49.083Z", "scannedAt": "2025-06-23T05:59:49.083Z"}}, {"id": "nodejs-ecosystem", "source": "project", "protocol": "knowledge", "name": "Nodejs Ecosystem 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ecommerce-backend-engineer/knowledge/nodejs-ecosystem.knowledge.md", "metadata": {"createdAt": "2025-06-23T05:59:49.083Z", "updatedAt": "2025-06-23T05:59:49.083Z", "scannedAt": "2025-06-23T05:59:49.083Z"}}], "stats": {"totalResources": 8, "byProtocol": {"role": 1, "thought": 1, "execution": 3, "knowledge": 3}, "bySource": {"project": 8}}}