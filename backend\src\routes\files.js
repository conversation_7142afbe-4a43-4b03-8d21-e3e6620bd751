const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const router = express.Router();

// 确保上传目录存在
const uploadDir = 'uploads';
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置multer
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'file-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req, file, cb) => {
  if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
    cb(null, true);
  } else {
    cb(new Error('只支持CSV文件'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1
  }
});

// 文件上传路由
router.post('/upload', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '请选择要上传的文件',
        timestamp: new Date().toISOString()
      });
    }
    
    const fileInfo = {
      fileId: req.file.filename.replace(path.extname(req.file.filename), ''),
      originalName: req.file.originalname,
      fileName: req.file.filename,
      filePath: req.file.path,
      fileSize: req.file.size,
      mimeType: req.file.mimetype,
      uploadTime: new Date().toISOString()
    };
    
    res.json({
      code: 200,
      message: '文件上传成功',
      data: fileInfo,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '文件上传失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 获取文件列表
router.get('/list', (req, res) => {
  res.json({
    code: 200,
    message: '文件列表功能开发中',
    data: {
      files: [],
      pagination: {
        page: 1,
        pageSize: 20,
        total: 0,
        totalPages: 0
      }
    },
    timestamp: new Date().toISOString()
  });
});

// 获取文件详情
router.get('/:fileId', (req, res) => {
  const { fileId } = req.params;
  
  res.json({
    code: 200,
    message: '文件详情功能开发中',
    data: {
      fileId,
      status: 'pending'
    },
    timestamp: new Date().toISOString()
  });
});

// 删除文件
router.delete('/:fileId', (req, res) => {
  const { fileId } = req.params;
  
  res.json({
    code: 200,
    message: '文件删除功能开发中',
    data: {
      fileId,
      deleted: false
    },
    timestamp: new Date().toISOString()
  });
});

// 文件处理进度
router.get('/:fileId/progress', (req, res) => {
  const { fileId } = req.params;
  
  res.json({
    code: 200,
    message: '获取处理进度成功',
    data: {
      fileId,
      status: 'pending',
      progress: 0,
      currentStep: 'waiting',
      message: '等待处理'
    },
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
