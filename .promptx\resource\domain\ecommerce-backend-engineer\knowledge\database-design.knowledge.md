# 数据库设计专业知识

## MongoDB文档数据库设计

### 电商运营工具数据模型

#### 用户集合 (users)
```javascript
{
  _id: ObjectId("..."),
  username: "user123",
  email: "<EMAIL>",
  passwordHash: "...",
  profile: {
    firstName: "张",
    lastName: "三",
    company: "某某电商",
    role: "运营经理"
  },
  settings: {
    defaultPlatform: "taobao",
    timezone: "Asia/Shanghai",
    currency: "CNY"
  },
  subscription: {
    plan: "premium",
    startDate: ISODate("2024-01-01"),
    endDate: ISODate("2024-12-31"),
    status: "active"
  },
  createdAt: ISODate("2024-01-01"),
  updatedAt: ISODate("2024-01-01")
}
```

#### 项目集合 (projects)
```javascript
{
  _id: ObjectId("..."),
  userId: ObjectId("..."),
  name: "2024年Q1运营分析",
  description: "第一季度电商运营数据分析项目",
  platform: "taobao",
  settings: {
    defaultCostRate: 0.3,
    defaultProfitTarget: 0.25,
    currency: "CNY"
  },
  status: "active",
  createdAt: ISODate("2024-01-01"),
  updatedAt: ISODate("2024-01-01")
}
```

#### 文件集合 (files)
```javascript
{
  _id: ObjectId("..."),
  userId: ObjectId("..."),
  projectId: ObjectId("..."),
  originalName: "orders_202401.csv",
  fileName: "file_123456.csv",
  filePath: "/uploads/2024/01/file_123456.csv",
  fileSize: 1024000,
  mimeType: "text/csv",
  platform: "taobao",
  uploadStatus: "completed",
  processStatus: "processed",
  metadata: {
    recordCount: 1000,
    validRecords: 980,
    errorRecords: 20,
    columns: ["订单编号", "商品标题", "SKU", "数量", "单价"],
    encoding: "utf-8"
  },
  processedAt: ISODate("2024-01-01"),
  createdAt: ISODate("2024-01-01")
}
```

#### 订单数据集合 (orders)
```javascript
{
  _id: ObjectId("..."),
  fileId: ObjectId("..."),
  userId: ObjectId("..."),
  projectId: ObjectId("..."),
  
  // 原始订单信息
  orderId: "TB123456789",
  platform: "taobao",
  orderTime: ISODate("2024-01-01T10:00:00Z"),
  
  // 商品信息
  sku: "SKU001",
  productName: "商品A",
  category: "电子产品",
  specification: "红色/64GB",
  
  // 交易信息
  quantity: 2,
  unitPrice: 299.00,
  totalAmount: 598.00,
  actualAmount: 568.00,
  discount: 30.00,
  shippingFee: 10.00,
  
  // 成本信息
  unitCost: 180.00,
  totalCost: 360.00,
  
  // 计算字段
  revenue: 568.00,
  profit: 208.00,
  profitRate: 36.62,
  
  // 客户信息
  buyerId: "buyer123",
  receiverProvince: "广东省",
  receiverCity: "深圳市",
  
  // 状态信息
  orderStatus: "completed",
  paymentStatus: "paid",
  shippingStatus: "delivered",
  
  createdAt: ISODate("2024-01-01"),
  updatedAt: ISODate("2024-01-01")
}
```

#### 计算结果集合 (calculations)
```javascript
{
  _id: ObjectId("..."),
  userId: ObjectId("..."),
  projectId: ObjectId("..."),
  type: "roi", // roi, pricing, profit_analysis
  
  // 输入参数
  input: {
    adCost: 1000,
    revenue: 5000,
    productCost: 2000,
    timeRange: {
      startDate: ISODate("2024-01-01"),
      endDate: ISODate("2024-01-31")
    }
  },
  
  // 计算结果
  result: {
    roi: 200.0,
    roas: 5.0,
    profit: 2000,
    profitRate: 40.0
  },
  
  // 元数据
  calculatedAt: ISODate("2024-01-01"),
  version: "1.0"
}
```

### 索引设计策略

#### 复合索引设计
```javascript
// 用户相关查询优化
db.orders.createIndex({ userId: 1, orderTime: -1 });
db.files.createIndex({ userId: 1, createdAt: -1 });
db.calculations.createIndex({ userId: 1, type: 1, calculatedAt: -1 });

// 项目相关查询优化
db.orders.createIndex({ projectId: 1, sku: 1 });
db.files.createIndex({ projectId: 1, processStatus: 1 });

// 业务查询优化
db.orders.createIndex({ sku: 1, orderTime: -1 });
db.orders.createIndex({ platform: 1, orderStatus: 1 });
db.orders.createIndex({ orderTime: -1, profitRate: -1 });

// 文本搜索索引
db.orders.createIndex({ 
  productName: "text", 
  sku: "text" 
}, {
  weights: { productName: 10, sku: 5 }
});
```

#### 分片策略
```javascript
// 基于用户ID分片
sh.shardCollection("ecommerce.orders", { userId: 1, orderTime: 1 });
sh.shardCollection("ecommerce.files", { userId: 1 });

// 基于时间分片（适用于大量历史数据）
sh.shardCollection("ecommerce.calculations", { calculatedAt: 1 });
```

## Redis缓存设计

### 缓存键命名规范
```javascript
// 用户相关缓存
const USER_CACHE_KEY = "user:{userId}";
const USER_SESSION_KEY = "session:{sessionId}";
const USER_PERMISSIONS_KEY = "permissions:{userId}";

// 项目相关缓存
const PROJECT_CACHE_KEY = "project:{projectId}";
const PROJECT_STATS_KEY = "stats:project:{projectId}";

// 计算结果缓存
const ROI_CACHE_KEY = "roi:{userId}:{hash}";
const PRICING_CACHE_KEY = "pricing:{userId}:{hash}";

// 文件处理缓存
const FILE_PROCESS_KEY = "process:file:{fileId}";
const FILE_PROGRESS_KEY = "progress:file:{fileId}";

// 统计数据缓存
const SKU_STATS_KEY = "stats:sku:{projectId}:{dateRange}";
const DAILY_STATS_KEY = "stats:daily:{userId}:{date}";
```

### 缓存数据结构

#### 用户会话缓存 (Hash)
```javascript
// 存储用户会话信息
HSET session:abc123 userId 12345
HSET session:abc123 username "user123"
HSET session:abc123 role "admin"
HSET session:abc123 loginTime "2024-01-01T10:00:00Z"
EXPIRE session:abc123 86400  // 24小时过期
```

#### 计算结果缓存 (String)
```javascript
// ROI计算结果缓存
const roiResult = {
  roi: 200.0,
  roas: 5.0,
  profit: 2000,
  profitRate: 40.0,
  calculatedAt: "2024-01-01T10:00:00Z"
};

SET roi:12345:hash123 JSON.stringify(roiResult)
EXPIRE roi:12345:hash123 3600  // 1小时过期
```

#### 文件处理进度 (Hash)
```javascript
// 文件处理进度跟踪
HSET progress:file:123 status "processing"
HSET progress:file:123 totalRecords 1000
HSET progress:file:123 processedRecords 500
HSET progress:file:123 errorRecords 5
HSET progress:file:123 progress 50
EXPIRE progress:file:123 7200  // 2小时过期
```

#### 热门SKU排行 (Sorted Set)
```javascript
// SKU销量排行榜
ZADD sku:sales:ranking:202401 1000 "SKU001"
ZADD sku:sales:ranking:202401 800 "SKU002"
ZADD sku:sales:ranking:202401 600 "SKU003"

// 获取TOP10
ZREVRANGE sku:sales:ranking:202401 0 9 WITHSCORES
```

#### 实时统计计数器 (String)
```javascript
// 日活跃用户计数
INCR stats:dau:20240101
EXPIRE stats:dau:20240101 86400

// API调用次数统计
INCR stats:api:roi:20240101
EXPIRE stats:api:roi:20240101 86400
```

### 缓存策略实现

#### 缓存穿透防护
```javascript
class CacheService {
  static async getWithFallback(key, fallbackFn, ttl = 3600) {
    // 尝试从缓存获取
    let value = await redis.get(key);
    
    if (value !== null) {
      return value === 'NULL' ? null : JSON.parse(value);
    }
    
    // 缓存未命中，执行回调函数
    const result = await fallbackFn();
    
    // 存入缓存，空值也缓存防止穿透
    const cacheValue = result === null ? 'NULL' : JSON.stringify(result);
    await redis.setex(key, ttl, cacheValue);
    
    return result;
  }
}
```

#### 缓存更新策略
```javascript
class OrderService {
  static async updateOrder(orderId, updateData) {
    // 更新数据库
    const result = await Order.findByIdAndUpdate(orderId, updateData, { new: true });
    
    // 删除相关缓存
    const cacheKeys = [
      `order:${orderId}`,
      `stats:user:${result.userId}`,
      `stats:project:${result.projectId}`,
      `sku:stats:${result.sku}`
    ];
    
    await redis.del(...cacheKeys);
    
    return result;
  }
}
```

## 数据库性能优化

### 查询优化技巧

#### 聚合管道优化
```javascript
// 优化前：多次查询
const orders = await Order.find({ userId, orderTime: { $gte: startDate, $lte: endDate } });
const totalRevenue = orders.reduce((sum, order) => sum + order.revenue, 0);
const totalProfit = orders.reduce((sum, order) => sum + order.profit, 0);

// 优化后：单次聚合查询
const stats = await Order.aggregate([
  {
    $match: {
      userId: ObjectId(userId),
      orderTime: { $gte: startDate, $lte: endDate }
    }
  },
  {
    $group: {
      _id: null,
      totalRevenue: { $sum: "$revenue" },
      totalProfit: { $sum: "$profit" },
      orderCount: { $sum: 1 },
      avgOrderValue: { $avg: "$revenue" }
    }
  }
]);
```

#### 分页查询优化
```javascript
// 避免使用skip进行深度分页
class PaginationService {
  static async getOrdersPaginated(userId, lastOrderId = null, limit = 20) {
    const query = { userId };
    
    // 使用游标分页而不是skip
    if (lastOrderId) {
      query._id = { $lt: ObjectId(lastOrderId) };
    }
    
    const orders = await Order.find(query)
      .sort({ _id: -1 })
      .limit(limit + 1);
    
    const hasMore = orders.length > limit;
    if (hasMore) orders.pop();
    
    return {
      data: orders,
      hasMore,
      nextCursor: orders.length > 0 ? orders[orders.length - 1]._id : null
    };
  }
}
```

### 连接池配置
```javascript
// MongoDB连接池优化
const mongooseOptions = {
  maxPoolSize: 10,        // 最大连接数
  minPoolSize: 2,         // 最小连接数
  maxIdleTimeMS: 30000,   // 连接最大空闲时间
  serverSelectionTimeoutMS: 5000,  // 服务器选择超时
  socketTimeoutMS: 45000, // Socket超时时间
  bufferMaxEntries: 0,    // 禁用缓冲
  bufferCommands: false   // 禁用命令缓冲
};

// Redis连接池配置
const redisOptions = {
  host: process.env.REDIS_HOST,
  port: process.env.REDIS_PORT,
  password: process.env.REDIS_PASSWORD,
  db: 0,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4,
  maxmemoryPolicy: 'allkeys-lru'
};
```

### 数据归档策略
```javascript
// 历史数据归档
class DataArchiveService {
  static async archiveOldOrders(cutoffDate) {
    const session = await mongoose.startSession();
    
    try {
      await session.withTransaction(async () => {
        // 查找需要归档的订单
        const oldOrders = await Order.find({
          orderTime: { $lt: cutoffDate }
        }).session(session);
        
        // 移动到归档集合
        if (oldOrders.length > 0) {
          await ArchivedOrder.insertMany(oldOrders, { session });
          await Order.deleteMany({
            _id: { $in: oldOrders.map(o => o._id) }
          }, { session });
        }
        
        console.log(`归档了 ${oldOrders.length} 条历史订单`);
      });
    } finally {
      await session.endSession();
    }
  }
}
```
