const express = require('express');
const router = express.Router();

// 投产比计算路由
router.post('/roi', (req, res) => {
  try {
    const { adCost, revenue, productCost = 0 } = req.body;
    
    // 基础验证
    if (!adCost || !revenue) {
      return res.status(400).json({
        code: 400,
        message: '广告费用和销售额不能为空',
        timestamp: new Date().toISOString()
      });
    }
    
    if (adCost <= 0 || revenue <= 0) {
      return res.status(400).json({
        code: 400,
        message: '广告费用和销售额必须大于0',
        timestamp: new Date().toISOString()
      });
    }
    
    // 计算ROI
    const roi = ((revenue - adCost) / adCost) * 100;
    const roas = revenue / adCost;
    const grossProfit = revenue - productCost;
    const netProfit = grossProfit - adCost;
    const profitRate = (netProfit / revenue) * 100;
    
    res.json({
      code: 200,
      message: '计算成功',
      data: {
        roi: parseFloat(roi.toFixed(2)),
        roas: parseFloat(roas.toFixed(2)),
        grossProfit: parseFloat(grossProfit.toFixed(2)),
        netProfit: parseFloat(netProfit.toFixed(2)),
        profitRate: parseFloat(profitRate.toFixed(2)),
        calculatedAt: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '计算失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 定价计算路由
router.post('/pricing', (req, res) => {
  try {
    const {
      cost,
      targetProfitRate,
      operatingCost = 0,
      platformFee = 0,
      competitorPrice = null
    } = req.body;
    
    // 基础验证
    if (!cost || targetProfitRate === undefined) {
      return res.status(400).json({
        code: 400,
        message: '成本和目标利润率不能为空',
        timestamp: new Date().toISOString()
      });
    }
    
    if (cost <= 0) {
      return res.status(400).json({
        code: 400,
        message: '成本必须大于0',
        timestamp: new Date().toISOString()
      });
    }
    
    if (targetProfitRate < 0 || targetProfitRate > 100) {
      return res.status(400).json({
        code: 400,
        message: '目标利润率必须在0-100之间',
        timestamp: new Date().toISOString()
      });
    }
    
    // 计算定价
    const totalCost = cost + operatingCost + platformFee;
    const suggestedPrice = totalCost / (1 - targetProfitRate / 100);
    const actualProfitRate = ((suggestedPrice - totalCost) / suggestedPrice) * 100;
    
    // 竞品价格分析
    let competitiveAnalysis = null;
    if (competitorPrice) {
      const priceDiff = suggestedPrice - competitorPrice;
      const priceDiffPercent = (priceDiff / competitorPrice) * 100;
      
      if (priceDiffPercent > 20) {
        competitiveAnalysis = {
          status: 'higher',
          message: '价格偏高，可能影响竞争力',
          difference: priceDiffPercent
        };
      } else if (priceDiffPercent < -20) {
        competitiveAnalysis = {
          status: 'lower',
          message: '价格偏低，利润空间可能不足',
          difference: priceDiffPercent
        };
      } else {
        competitiveAnalysis = {
          status: 'competitive',
          message: '价格具有竞争力',
          difference: priceDiffPercent
        };
      }
    }
    
    res.json({
      code: 200,
      message: '计算成功',
      data: {
        suggestedPrice: parseFloat(suggestedPrice.toFixed(2)),
        totalCost: parseFloat(totalCost.toFixed(2)),
        profit: parseFloat((suggestedPrice - totalCost).toFixed(2)),
        profitRate: parseFloat(actualProfitRate.toFixed(2)),
        competitiveAnalysis,
        calculatedAt: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '计算失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 获取计算历史
router.get('/history', (req, res) => {
  res.json({
    code: 200,
    message: '计算历史功能开发中',
    data: [],
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
