<execution>
  <constraint>
    ## API设计客观约束
    
    ### RESTful约束
    - **资源导向**: URL必须表示资源，不能包含动词
    - **HTTP方法**: 必须正确使用GET、POST、PUT、DELETE
    - **状态码**: 必须使用标准HTTP状态码
    - **无状态**: API必须是无状态的，不依赖服务器状态
    
    ### 电商业务约束
    - **数据一致性**: 订单、支付、库存数据必须保持一致
    - **并发控制**: 库存扣减、订单创建需要并发控制
    - **业务规则**: 必须遵循电商业务规则和流程
    - **数据安全**: 用户数据、交易数据必须安全处理
  </constraint>
  
  <rule>
    ## API设计强制规则
    
    ### URL设计规则
    - **资源命名**: 使用名词复数形式，如 /api/v1/orders
    - **层级关系**: 体现资源层级关系，如 /api/v1/users/{id}/orders
    - **版本控制**: 必须包含版本号，如 /api/v1/
    - **小写字母**: URL必须使用小写字母和连字符
    
    ### 请求响应规则
    - **Content-Type**: 必须设置正确的Content-Type
    - **请求体**: POST/PUT请求必须有请求体验证
    - **响应格式**: 统一的JSON响应格式
    - **错误处理**: 统一的错误响应格式
    
    ### 安全规则
    - **身份验证**: 受保护的API必须验证JWT Token
    - **权限检查**: 必须检查用户操作权限
    - **数据验证**: 必须验证所有输入参数
    - **敏感数据**: 响应中不能包含敏感信息
  </rule>
  
  <guideline>
    ## API设计指导原则
    
    ### 设计原则
    - **一致性**: 保持API设计风格一致
    - **简洁性**: API接口简洁明了，易于理解
    - **可扩展性**: 设计时考虑未来扩展需求
    - **向后兼容**: 新版本API保持向后兼容
    
    ### 性能优化
    - **分页查询**: 列表接口必须支持分页
    - **字段筛选**: 支持字段筛选减少数据传输
    - **缓存策略**: 合理使用缓存提高响应速度
    - **压缩传输**: 启用Gzip压缩减少传输量
    
    ### 用户体验
    - **错误信息**: 提供清晰的错误信息和解决建议
    - **文档完善**: 提供详细的API文档和示例
    - **调试友好**: 提供调试信息帮助开发者
    - **监控告警**: 提供API监控和告警机制
  </guideline>
  
  <process>
    ## 电商运营工具API设计流程
    
    ### 核心API设计
    
    #### 1. 投产比计算API
    ```
    POST /api/v1/calculations/roi
    
    请求体:
    {
      "adCost": 1000,        // 广告费用
      "revenue": 5000,       // 销售额
      "productCost": 2000,   // 商品成本
      "timeRange": {         // 时间范围
        "startDate": "2024-01-01",
        "endDate": "2024-01-31"
      },
      "channel": "taobao"    // 广告渠道
    }
    
    响应:
    {
      "code": 200,
      "message": "计算成功",
      "data": {
        "roi": 200.0,        // ROI百分比
        "roas": 5.0,         // ROAS倍数
        "profit": 2000,      // 利润金额
        "profitRate": 40.0   // 利润率
      }
    }
    ```
    
    #### 2. 定价计算API
    ```
    POST /api/v1/calculations/pricing
    
    请求体:
    {
      "cost": 100,           // 成本
      "targetProfitRate": 30, // 目标利润率
      "operatingCost": 10,   // 运营成本
      "platformFee": 5,      // 平台费用
      "competitorPrice": 150 // 竞品价格
    }
    
    响应:
    {
      "code": 200,
      "message": "计算成功",
      "data": {
        "suggestedPrice": 142.86,  // 建议售价
        "minPrice": 130,           // 最低价格
        "maxPrice": 160,           // 最高价格
        "profitMargin": 30,        // 利润率
        "competitiveness": "good"   // 竞争力评估
      }
    }
    ```
    
    #### 3. CSV文件上传API
    ```
    POST /api/v1/files/upload
    Content-Type: multipart/form-data
    
    请求:
    - file: CSV文件
    - platform: 平台类型 (taobao/tmall/jd)
    
    响应:
    {
      "code": 200,
      "message": "上传成功",
      "data": {
        "fileId": "file_123456",
        "fileName": "orders.csv",
        "fileSize": 1024000,
        "recordCount": 1000,
        "uploadTime": "2024-01-01T10:00:00Z"
      }
    }
    ```
    
    #### 4. 利润分析API
    ```
    POST /api/v1/analysis/profit
    
    请求体:
    {
      "fileId": "file_123456",
      "analysisType": "sku",     // 分析类型
      "timeRange": {
        "startDate": "2024-01-01",
        "endDate": "2024-01-31"
      }
    }
    
    响应:
    {
      "code": 200,
      "message": "分析完成",
      "data": {
        "totalRevenue": 100000,    // 总销售额
        "totalProfit": 30000,      // 总利润
        "profitRate": 30.0,        // 利润率
        "topSkus": [               // 热销SKU
          {
            "sku": "SKU001",
            "name": "商品A",
            "sales": 5000,
            "profit": 1500,
            "quantity": 100
          }
        ]
      }
    }
    ```
    
    #### 5. SKU销量占比API
    ```
    GET /api/v1/analysis/sku-distribution
    
    查询参数:
    - fileId: 文件ID
    - dimension: 维度 (sales/revenue/profit)
    - limit: 返回数量限制
    
    响应:
    {
      "code": 200,
      "message": "查询成功",
      "data": {
        "chartData": [
          {
            "sku": "SKU001",
            "name": "商品A",
            "value": 5000,
            "percentage": 25.0
          }
        ],
        "summary": {
          "totalValue": 20000,
          "skuCount": 50,
          "top10Percentage": 80.0
        }
      }
    }
    ```
    
    ### 通用API设计
    
    #### 统一响应格式
    ```json
    {
      "code": 200,           // 状态码
      "message": "操作成功", // 消息
      "data": {},           // 数据
      "timestamp": "2024-01-01T10:00:00Z",
      "requestId": "req_123456"
    }
    ```
    
    #### 错误响应格式
    ```json
    {
      "code": 400,
      "message": "参数错误",
      "error": {
        "type": "VALIDATION_ERROR",
        "details": [
          {
            "field": "adCost",
            "message": "广告费用必须大于0"
          }
        ]
      },
      "timestamp": "2024-01-01T10:00:00Z",
      "requestId": "req_123456"
    }
    ```
    
    #### 分页响应格式
    ```json
    {
      "code": 200,
      "message": "查询成功",
      "data": {
        "items": [],
        "pagination": {
          "page": 1,
          "pageSize": 20,
          "total": 100,
          "totalPages": 5
        }
      }
    }
    ```
  </process>
  
  <criteria>
    ## API设计质量标准
    
    ### 规范性标准
    - **RESTful合规**: 100%符合RESTful设计原则
    - **命名规范**: URL和字段命名符合约定
    - **文档完整**: API文档覆盖率100%
    - **示例完整**: 每个API都有完整的请求响应示例
    
    ### 功能性标准
    - **业务覆盖**: 覆盖所有核心业务功能
    - **参数验证**: 所有输入参数都有验证
    - **错误处理**: 完善的错误处理和错误信息
    - **边界处理**: 处理各种边界情况和异常
    
    ### 性能标准
    - **响应时间**: 95%的API响应时间 ≤ 1秒
    - **并发能力**: 支持1000+并发请求
    - **数据传输**: 合理的数据传输量
    - **缓存效果**: 缓存命中率 ≥ 80%
    
    ### 安全标准
    - **身份验证**: 100%的受保护API需要认证
    - **权限控制**: 细粒度的权限控制
    - **数据验证**: 严格的输入数据验证
    - **安全审计**: 完整的API访问日志
  </criteria>
</execution>
