const express = require('express');
const router = express.Router();

// 临时路由，后续会实现完整的认证功能
router.get('/test', (req, res) => {
  res.json({
    message: '认证路由正常工作',
    timestamp: new Date().toISOString()
  });
});

// 注册路由
router.post('/register', (req, res) => {
  res.json({
    message: '注册功能开发中',
    status: 'coming_soon'
  });
});

// 登录路由
router.post('/login', (req, res) => {
  res.json({
    message: '登录功能开发中',
    status: 'coming_soon'
  });
});

// 登出路由
router.post('/logout', (req, res) => {
  res.json({
    message: '登出功能开发中',
    status: 'coming_soon'
  });
});

module.exports = router;
