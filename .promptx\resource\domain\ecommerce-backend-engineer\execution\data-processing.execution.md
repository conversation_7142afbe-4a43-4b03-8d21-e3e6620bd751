<execution>
  <constraint>
    ## 数据处理客观约束
    
    ### 文件处理约束
    - **文件大小**: CSV文件最大10MB，防止内存溢出
    - **文件格式**: 仅支持UTF-8编码的CSV文件
    - **并发限制**: 同时处理的文件数量限制为5个
    - **处理时间**: 单个文件处理时间不超过30秒
    
    ### 数据量约束
    - **记录数量**: 单个CSV文件最大10万条记录
    - **字段数量**: 最大支持50个字段
    - **内存使用**: 文件处理内存使用不超过256MB
    - **存储空间**: 处理后的数据存储空间限制
    
    ### 平台兼容约束
    - **字段差异**: 不同电商平台CSV格式差异
    - **编码问题**: 不同平台可能使用不同编码
    - **日期格式**: 各平台日期时间格式不统一
    - **数值格式**: 金额、数量等数值格式差异
  </constraint>
  
  <rule>
    ## 数据处理强制规则
    
    ### 文件验证规则
    - **格式检查**: 必须验证文件是否为有效CSV格式
    - **大小检查**: 必须检查文件大小是否超限
    - **编码检查**: 必须检查文件编码是否为UTF-8
    - **病毒扫描**: 上传文件必须进行安全扫描
    
    ### 数据清洗规则
    - **空值处理**: 必须处理空值和缺失数据
    - **重复数据**: 必须检测和处理重复记录
    - **数据类型**: 必须验证数据类型正确性
    - **数值范围**: 必须验证数值是否在合理范围内
    
    ### 错误处理规则
    - **异常记录**: 必须记录处理失败的数据行
    - **错误日志**: 必须详细记录错误信息
    - **回滚机制**: 处理失败时必须能够回滚
    - **用户通知**: 必须通知用户处理结果
  </rule>
  
  <guideline>
    ## 数据处理指导原则
    
    ### 性能优化
    - **流式处理**: 建议使用流式处理大文件
    - **批量操作**: 推荐批量插入数据库提高效率
    - **异步处理**: 建议异步处理耗时操作
    - **缓存策略**: 推荐缓存处理结果
    
    ### 数据质量
    - **数据验证**: 建议多层次数据验证
    - **数据标准化**: 推荐统一数据格式
    - **数据完整性**: 建议检查数据完整性
    - **数据一致性**: 推荐保证数据一致性
    
    ### 用户体验
    - **进度反馈**: 建议实时反馈处理进度
    - **错误提示**: 推荐清晰的错误提示信息
    - **预览功能**: 建议提供数据预览功能
    - **导出功能**: 推荐支持处理结果导出
  </guideline>
  
  <process>
    ## CSV数据处理流程
    
    ### 文件上传处理
    ```mermaid
    flowchart TD
        A[文件上传] --> B[文件验证]
        B --> C{验证通过?}
        C -->|否| D[返回错误信息]
        C -->|是| E[保存临时文件]
        E --> F[生成文件ID]
        F --> G[返回上传成功]
    ```
    
    ### 数据解析流程
    ```mermaid
    flowchart TD
        A[开始解析] --> B[检测平台类型]
        B --> C[字段映射配置]
        C --> D[逐行解析数据]
        D --> E[数据类型转换]
        E --> F[数据验证]
        F --> G{验证通过?}
        G -->|否| H[记录错误行]
        G -->|是| I[数据清洗]
        I --> J[保存到数据库]
        H --> K[继续下一行]
        J --> K
        K --> L{还有数据?}
        L -->|是| D
        L -->|否| M[生成处理报告]
    ```
    
    ### 平台字段映射
    
    #### 淘宝/天猫平台
    ```javascript
    const taobaoMapping = {
      '订单编号': 'orderId',
      '商品标题': 'productName',
      '商品SKU': 'sku',
      '数量': 'quantity',
      '单价': 'price',
      '实付金额': 'actualAmount',
      '下单时间': 'orderTime',
      '收货人': 'receiver'
    };
    ```
    
    #### 京东平台
    ```javascript
    const jdMapping = {
      '订单号': 'orderId',
      '商品名称': 'productName',
      'SKU编码': 'sku',
      '购买数量': 'quantity',
      '商品单价': 'price',
      '订单金额': 'actualAmount',
      '下单日期': 'orderTime',
      '收货人姓名': 'receiver'
    };
    ```
    
    ### 数据清洗算法
    
    #### 1. 数据类型转换
    ```javascript
    function convertDataTypes(row, mapping) {
      const converted = {};
      
      // 数值类型转换
      if (row.price) {
        converted.price = parseFloat(row.price.replace(/[^\d.]/g, ''));
      }
      
      // 日期类型转换
      if (row.orderTime) {
        converted.orderTime = new Date(row.orderTime);
      }
      
      // 整数类型转换
      if (row.quantity) {
        converted.quantity = parseInt(row.quantity);
      }
      
      return converted;
    }
    ```
    
    #### 2. 数据验证
    ```javascript
    function validateData(row) {
      const errors = [];
      
      // 必填字段检查
      if (!row.orderId) {
        errors.push('订单号不能为空');
      }
      
      // 数值范围检查
      if (row.price <= 0) {
        errors.push('商品价格必须大于0');
      }
      
      if (row.quantity <= 0) {
        errors.push('商品数量必须大于0');
      }
      
      // 日期有效性检查
      if (row.orderTime && isNaN(row.orderTime.getTime())) {
        errors.push('订单时间格式无效');
      }
      
      return errors;
    }
    ```
    
    #### 3. 重复数据检测
    ```javascript
    function detectDuplicates(data) {
      const seen = new Set();
      const duplicates = [];
      
      data.forEach((row, index) => {
        const key = `${row.orderId}_${row.sku}`;
        if (seen.has(key)) {
          duplicates.push(index);
        } else {
          seen.add(key);
        }
      });
      
      return duplicates;
    }
    ```
    
    ### 利润计算逻辑
    
    #### 单品利润计算
    ```javascript
    function calculateItemProfit(item) {
      const {
        price,           // 售价
        quantity,        // 数量
        cost,           // 成本
        shippingFee,    // 运费
        platformFee,    // 平台费用
        promotionDiscount // 促销折扣
      } = item;
      
      // 实际收入
      const actualRevenue = (price * quantity) - promotionDiscount;
      
      // 总成本
      const totalCost = (cost * quantity) + shippingFee + platformFee;
      
      // 利润
      const profit = actualRevenue - totalCost;
      
      // 利润率
      const profitRate = (profit / actualRevenue) * 100;
      
      return {
        revenue: actualRevenue,
        cost: totalCost,
        profit: profit,
        profitRate: profitRate
      };
    }
    ```
    
    #### SKU销量统计
    ```javascript
    function calculateSkuStats(data) {
      const skuStats = {};
      
      data.forEach(item => {
        const sku = item.sku;
        
        if (!skuStats[sku]) {
          skuStats[sku] = {
            sku: sku,
            name: item.productName,
            totalQuantity: 0,
            totalRevenue: 0,
            totalProfit: 0,
            orderCount: 0
          };
        }
        
        const itemProfit = calculateItemProfit(item);
        
        skuStats[sku].totalQuantity += item.quantity;
        skuStats[sku].totalRevenue += itemProfit.revenue;
        skuStats[sku].totalProfit += itemProfit.profit;
        skuStats[sku].orderCount += 1;
      });
      
      return Object.values(skuStats);
    }
    ```
  </process>
  
  <criteria>
    ## 数据处理质量标准
    
    ### 处理效率标准
    - **处理速度**: 每秒处理1000条记录
    - **内存使用**: 处理过程内存使用 ≤ 256MB
    - **并发能力**: 同时处理5个文件
    - **响应时间**: 文件上传响应时间 ≤ 3秒
    
    ### 数据质量标准
    - **准确率**: 数据解析准确率 ≥ 99%
    - **完整性**: 数据完整性检查通过率 ≥ 95%
    - **一致性**: 数据格式一致性 = 100%
    - **有效性**: 数据有效性验证通过率 ≥ 98%
    
    ### 错误处理标准
    - **错误检测**: 100%检测数据异常
    - **错误记录**: 完整记录错误信息
    - **错误恢复**: 支持错误数据修复
    - **用户反馈**: 清晰的错误提示信息
    
    ### 安全性标准
    - **文件安全**: 100%文件安全扫描
    - **数据脱敏**: 敏感数据自动脱敏
    - **访问控制**: 严格的数据访问权限
    - **审计日志**: 完整的数据处理日志
  </criteria>
</execution>
