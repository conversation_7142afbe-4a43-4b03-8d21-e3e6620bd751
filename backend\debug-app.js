// 调试版本的应用启动文件
console.log('🔍 开始调试应用启动...');

try {
  console.log('1. 加载基础模块...');
  const express = require('express');
  const cors = require('cors');
  console.log('✅ Express 和 CORS 加载成功');

  console.log('2. 加载环境变量...');
  require('dotenv').config();
  console.log('✅ 环境变量加载成功');

  console.log('3. 加载工具模块...');
  const logger = require('./src/utils/logger');
  console.log('✅ Logger 加载成功');

  console.log('4. 加载中间件...');
  const { globalErrorHandler } = require('./src/middleware/errorHandler');
  const notFound = require('./src/middleware/notFound');
  console.log('✅ 中间件加载成功');

  console.log('5. 加载路由...');
  const authRoutes = require('./src/routes/auth');
  console.log('✅ Auth 路由加载成功');
  
  const calculationRoutes = require('./src/routes/calculations');
  console.log('✅ Calculations 路由加载成功');
  
  const fileRoutes = require('./src/routes/files');
  console.log('✅ Files 路由加载成功');
  
  const analysisRoutes = require('./src/routes/analysis');
  console.log('✅ Analysis 路由加载成功');
  
  const userRoutes = require('./src/routes/users');
  console.log('✅ Users 路由加载成功');

  console.log('6. 创建 Express 应用...');
  const app = express();

  console.log('7. 配置中间件...');
  app.use(cors());
  app.use(express.json());
  console.log('✅ 基础中间件配置成功');

  console.log('8. 配置路由...');
  app.get('/health', (req, res) => {
    res.json({
      status: 'OK',
      message: '调试服务器运行正常',
      timestamp: new Date().toISOString()
    });
  });

  app.use('/api/v1/auth', authRoutes);
  app.use('/api/v1/calculations', calculationRoutes);
  app.use('/api/v1/files', fileRoutes);
  app.use('/api/v1/analysis', analysisRoutes);
  app.use('/api/v1/users', userRoutes);
  console.log('✅ 路由配置成功');

  console.log('9. 配置错误处理...');
  app.use(notFound);
  app.use(globalErrorHandler);
  console.log('✅ 错误处理配置成功');

  console.log('10. 启动服务器...');
  const PORT = 3002; // 使用不同端口避免冲突
  
  const server = app.listen(PORT, () => {
    console.log(`🚀 调试服务器启动成功！`);
    console.log(`📍 地址: http://localhost:${PORT}`);
    console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
    console.log(`📚 API路由:`);
    console.log(`   - POST /api/v1/calculations/roi`);
    console.log(`   - POST /api/v1/calculations/pricing`);
    console.log(`   - POST /api/v1/files/upload`);
    console.log(`   - GET  /api/v1/analysis/sku-distribution`);
  });

  // 错误处理
  server.on('error', (error) => {
    console.error('❌ 服务器启动失败:', error);
  });

} catch (error) {
  console.error('❌ 应用启动过程中发生错误:');
  console.error('错误类型:', error.name);
  console.error('错误信息:', error.message);
  console.error('错误堆栈:', error.stack);
  process.exit(1);
}
